<?php
/**
 * Stripe Plan Functions
 *
 * This file contains functions related to Stripe subscription plans.
 */

// Include required files
require_once __DIR__ . '/StripeLogger.php';
require_once __DIR__ . '/subscription_features.php';
require_once __DIR__ . '/../language_config.php';

/**
 * Get subscription plans
 */
function getSubscriptionPlans() {
    global $link;

    try {
        // Get plans from database
        $plans = getPlansFromDatabase($link);

        // If no plans in database, get from Stripe API
        if (empty($plans)) {
            $plans = getPlansFromStripeAPI($link);
        }

        // Add features for all subscription levels
        $language = getSelectedLanguage(); // Get user's preferred language

        $response = [
            'plans' => $plans,
            'features' => [
                'free' => getSubscriptionFeatures('free', $language),
                'basic' => getSubscriptionFeatures('basic', $language),
                'advance' => getSubscriptionFeatures('advance', $language),
                'premium' => getSubscriptionFeatures('premium', $language)
            ]
        ];

        // Return plans with features
        echo json_encode(new SuccessResult($response));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE GET PLANS ERROR: " . $e->getMessage(), [
            'exception' => get_class($e)
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

/**
 * Get plans from database
 *
 * @param mysqli $link Database connection
 * @return array Array of plans
 */
function getPlansFromDatabase($link) {
    $query = "SELECT p.stripe_product_id, p.name, p.description, p.active,
              pr.stripe_price_id, pr.currency, pr.unit_amount, pr.`interval`, pr.interval_count
              FROM stripe_products p
              JOIN stripe_prices pr ON p.stripe_product_id = pr.stripe_product_id
              WHERE p.active = 1 AND pr.active = 1
              ORDER BY pr.unit_amount ASC";

    $result = mysqli_query($link, $query);

    if (!$result) {
        throw new Exception("Database error: " . mysqli_error($link));
    }

    $plans = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $plans[] = [
            'id' => $row['stripe_price_id'],
            'product_id' => $row['stripe_product_id'],
            'name' => $row['name'],
            'description' => $row['description'],
            'price' => $row['unit_amount'],
            'currency' => $row['currency'],
            'interval' => $row['interval'],
            'interval_count' => $row['interval_count']
        ];
    }

    return $plans;
}

/**
 * Get plans from Stripe API
 *
 * @param mysqli $link Database connection
 * @return array Array of plans
 */
function getPlansFromStripeAPI($link) {
    // Get all active products
    $products = \Stripe\Product::all(['active' => true]);

    // Get all active prices
    $prices = \Stripe\Price::all(['active' => true]);

    // Map prices to products
    $productPrices = [];
    foreach ($prices->data as $price) {
        if (!isset($productPrices[$price->product])) {
            $productPrices[$price->product] = [];
        }
        $productPrices[$price->product][] = $price;
    }

    // Build plans array
    $plans = [];
    foreach ($products->data as $product) {
        if (isset($productPrices[$product->id])) {
            foreach ($productPrices[$product->id] as $price) {
                // Only include recurring prices
                if (isset($price->recurring)) {
                    $plans[] = [
                        'id' => $price->id,
                        'product_id' => $product->id,
                        'name' => $product->name,
                        'description' => $product->description,
                        'price' => $price->unit_amount / 100, // Convert from cents to dollars
                        'currency' => $price->currency,
                        'interval' => $price->recurring->interval,
                        'interval_count' => $price->recurring->interval_count
                    ];
                }
            }
        }
    }

    // Sort plans by price
    usort($plans, function($a, $b) {
        return $a['price'] <=> $b['price'];
    });

    return $plans;
}

/**
 * Determine subscription level based on plan name
 *
 * @param string $planName The plan name
 * @return string The subscription level
 */
/**
 * Determine subscription level based on plan name
 *
 * This function maps plan names to standardized subscription levels:
 * - 'free': Free tier
 * - 'basic': Basic tier
 * - 'advance': Advance/Pro tier
 * - 'premium': Premium tier
 *
 * @param string $planName The plan name
 * @return string The subscription level
 */
function determineSubscriptionLevel($planName) {
    if (!$planName) {
        return 'free'; // Default to free if no plan name provided
    }

    $planName = strtolower(trim($planName));

    // Log the plan name for debugging
    StripeLogger::log(StripeLogLevel::DEBUG, "DETERMINING SUBSCRIPTION LEVEL FOR: '$planName'");

    // Check for exact matches first (more reliable)
    if ($planName === 'basic') {
        return 'basic';
    } elseif ($planName === 'premium') {
        return 'premium';
    } elseif ($planName === 'advance' || $planName === 'pro') {
        return 'advance';
    }

    // Then check for partial matches
    if (strpos($planName, 'basic') !== false) {
        return 'basic';
    } elseif (strpos($planName, 'premium') !== false) {
        return 'premium';
    } elseif (strpos($planName, 'pro') !== false || strpos($planName, 'advance') !== false) {
        return 'advance'; // Handle both 'pro' and 'advance' names
    } elseif (strpos($planName, 'free') !== false) {
        return 'free';
    } else {
        // If no match found, try to determine by looking at common patterns
        if (preg_match('/^b/i', $planName)) { // Names starting with B
            return 'basic';
        } elseif (preg_match('/^p/i', $planName)) { // Names starting with P
            if (preg_match('/^pr/i', $planName)) { // Names starting with Pr (Pro)
                return 'advance';
            } else {
                return 'premium'; // Assume Premium for other P names
            }
        } elseif (preg_match('/^a/i', $planName)) { // Names starting with A
            return 'advance';
        }

        // Default to basic if no pattern matches
        StripeLogger::log(StripeLogLevel::WARNING, "NO LEVEL MATCH FOR PLAN NAME: '$planName', defaulting to 'basic'");
        return 'basic';
    }
}

/**
 * Get available downgrade options for a user with an active subscription
 *
 * This function returns only plans that are less expensive than the user's current plan
 * and have the same billing interval (monthly/yearly)
 */
function getDowngradeOptions() {
    global $link;

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }

    try {
        // Get user's active subscription
        $query = "SELECT sus.id, sus.stripe_subscription_id, sus.plan_id, sus.plan_name,
                 sus.plan_amount, sus.plan_interval
                 FROM stripe_user_subscriptions sus
                 WHERE sus.user_id = ? AND sus.status IN ('active', 'trialing')
                 ORDER BY sus.created_at DESC LIMIT 1";

        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $subscription = mysqli_fetch_assoc($result);

        if (!$subscription) {
            // No active subscription found
            echo json_encode(new ErrorResult("No active subscription found. Please purchase a subscription first.", 404));
            return;
        }

        // Get current plan details
        $currentPlanAmount = $subscription['plan_amount'];
        $currentPlanInterval = $subscription['plan_interval'];
        $currentPlanName = $subscription['plan_name'];
        $currentPlanId = $subscription['plan_id'];

        // Get current product ID from the plan ID
        $productQuery = "SELECT stripe_product_id FROM stripe_prices WHERE stripe_price_id = ?";
        $productStmt = mysqli_prepare($link, $productQuery);

        if (!$productStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($productStmt, "s", $currentPlanId);
        mysqli_stmt_execute($productStmt);
        $productResult = mysqli_stmt_get_result($productStmt);
        $productData = mysqli_fetch_assoc($productResult);

        $currentProductId = null;
        if ($productData && isset($productData['stripe_product_id'])) {
            $currentProductId = $productData['stripe_product_id'];
        }

        // Get current product name
        $currentProductName = null;
        if ($currentProductId) {
            $productNameQuery = "SELECT name FROM stripe_products WHERE stripe_product_id = ?";
            $productNameStmt = mysqli_prepare($link, $productNameQuery);

            if ($productNameStmt) {
                mysqli_stmt_bind_param($productNameStmt, "s", $currentProductId);
                mysqli_stmt_execute($productNameStmt);
                $productNameResult = mysqli_stmt_get_result($productNameStmt);
                $productNameData = mysqli_fetch_assoc($productNameResult);

                if ($productNameData && isset($productNameData['name'])) {
                    $currentProductName = $productNameData['name'];
                }
            }
        }

        // Log current plan details for debugging
        StripeLogger::log(StripeLogLevel::DEBUG, "CURRENT PLAN DETAILS FOR DOWNGRADE - User ID: $userId, Plan: $currentPlanName, Product: $currentProductName, Product ID: $currentProductId, Amount: $currentPlanAmount, Interval: $currentPlanInterval");

        // Define the hierarchy of subscription levels
        $levels = ['free' => 0, 'basic' => 1, 'advance' => 2, 'premium' => 3];

        // Determine current subscription level based on product name
        $currentLevel = 'free';
        if ($currentProductName) {
            $currentLevel = determineSubscriptionLevel($currentProductName);
        } else {
            $currentLevel = determineSubscriptionLevel($currentPlanName);
        }

        $currentLevelRank = isset($levels[$currentLevel]) ? $levels[$currentLevel] : 0;

        StripeLogger::log(StripeLogLevel::DEBUG, "CURRENT SUBSCRIPTION LEVEL FOR DOWNGRADE - Level: $currentLevel, Rank: $currentLevelRank");

        // Get all available plans with the same interval but lower price
        $plansQuery = "SELECT p.stripe_product_id, p.name as product_name, p.description, p.active,
                      pr.stripe_price_id, pr.currency, pr.unit_amount, pr.`interval`, pr.interval_count
                      FROM stripe_products p
                      JOIN stripe_prices pr ON p.stripe_product_id = pr.stripe_product_id
                      WHERE p.active = 1 AND pr.active = 1
                      AND pr.`interval` = ? AND pr.unit_amount < ?
                      ORDER BY pr.unit_amount DESC";

        $plansStmt = mysqli_prepare($link, $plansQuery);

        if (!$plansStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($plansStmt, "si", $currentPlanInterval, $currentPlanAmount);
        mysqli_stmt_execute($plansStmt);
        $plansResult = mysqli_stmt_get_result($plansStmt);

        $downgradePlans = [];
        $rowCount = 0;

        // Process all plans and filter based on subscription level
        while ($row = mysqli_fetch_assoc($plansResult)) {
            // Determine the level of this plan based on product name
            $planLevel = determineSubscriptionLevel($row['product_name']);
            $planLevelRank = isset($levels[$planLevel]) ? $levels[$planLevel] : 0;

            StripeLogger::log(StripeLogLevel::DEBUG, "CHECKING PLAN FOR DOWNGRADE: {$row['product_name']}, Level: $planLevel, Rank: $planLevelRank, Current Level Rank: $currentLevelRank");

            // Only include plans with a lower level than the current one
            if ($planLevelRank < $currentLevelRank) {
                $rowCount++;
                // Format price for display (convert from cents to dollars/TL)
                $formattedPrice = number_format($row['unit_amount'] / 100, 2);

                $downgradePlans[] = [
                    'id' => $row['stripe_price_id'],
                    'product_id' => $row['stripe_product_id'],
                    'name' => $row['product_name'],
                    'description' => $row['description'],
                    'price' => $formattedPrice,
                    'price_raw' => $row['unit_amount'],
                    'currency' => $row['currency'],
                    'interval' => $row['interval'],
                    'interval_count' => $row['interval_count'],
                    // Calculate price difference (negative for downgrade)
                    'price_difference' => number_format(($row['unit_amount'] - $currentPlanAmount) / 100, 2),
                    'price_difference_raw' => $row['unit_amount'] - $currentPlanAmount,
                    'level' => $planLevel,
                    'level_rank' => $planLevelRank,
                    'current_level_rank' => $currentLevelRank,
                    'effective_date' => date('Y-m-d', isset($subscription['current_period_end']) ? intval($subscription['current_period_end']) : (time() + (30 * 24 * 60 * 60)))
                ];

                StripeLogger::log(StripeLogLevel::DEBUG, "ADDED DOWNGRADE PLAN - Plan: {$row['product_name']}, Level: $planLevel, Amount: {$row['unit_amount']}");
            } else {
                StripeLogger::log(StripeLogLevel::DEBUG, "SKIPPED PLAN FOR DOWNGRADE - Plan: {$row['product_name']}, Level: $planLevel, Amount: {$row['unit_amount']} (not lower than current level)");
            }
        }

        // Log the results
        if ($rowCount > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "FOUND $rowCount DOWNGRADE OPTIONS for User ID: $userId");
        } else {
            StripeLogger::log(StripeLogLevel::WARNING, "NO DOWNGRADE OPTIONS FOUND for User ID: $userId with Plan: $currentPlanName, Level: $currentLevel");
        }

        // Return the downgrade options along with current subscription info
        echo json_encode(new SuccessResult([
            'current_subscription' => [
                'id' => $subscription['id'],
                'stripe_subscription_id' => $subscription['stripe_subscription_id'],
                'plan_id' => $subscription['plan_id'],
                'plan_name' => $currentPlanName,
                'product_name' => $currentProductName,
                'product_id' => $currentProductId,
                'plan_amount' => number_format($currentPlanAmount / 100, 2),
                'plan_amount_raw' => $currentPlanAmount,
                'plan_interval' => $currentPlanInterval,
                'level' => $currentLevel,
                'level_rank' => $currentLevelRank,
                'current_period_end' => date('Y-m-d', isset($subscription['current_period_end']) ? intval($subscription['current_period_end']) : (time() + (30 * 24 * 60 * 60)))
            ],
            'downgrade_options' => $downgradePlans,
            'note' => 'Downgrade will take effect at the end of the current billing period. No refunds will be issued for the current period.'
        ]));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "GET DOWNGRADE OPTIONS ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

/**
 * Get all subscription options for a user
 *
 * This function returns all available subscription plans and categorizes them as:
 * - 'current': The user's current plan
 * - 'upgrade': Plans that are more expensive/higher level than the current plan
 * - 'downgrade': Plans that are less expensive/lower level than the current plan
 * - 'other': Plans with a different billing interval (monthly vs yearly)
 *
 * If the user doesn't have an active subscription, all plans are categorized as 'available'
 */
function getSubscriptionOptions() {
    global $link;

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }

    try {
        // Variables to store current subscription info
        $hasActiveSubscription = false;
        $currentSubscription = null;
        $currentPlanId = null;
        $currentPlanAmount = 0;
        $currentPlanInterval = null;
        $currentPlanName = null;
        $currentProductId = null;
        $currentProductName = null;
        $currentLevel = 'free';
        $currentLevelRank = 0;

        // Define the hierarchy of subscription levels
        $levels = ['free' => 0, 'basic' => 1, 'advance' => 2, 'premium' => 3];

        // Check if user has an active subscription
        $query = "SELECT sus.id, sus.stripe_subscription_id, sus.plan_id, sus.plan_name,
                 sus.plan_amount, sus.plan_interval, sus.current_period_end
                 FROM stripe_user_subscriptions sus
                 WHERE sus.user_id = ? AND sus.status IN ('active', 'trialing')
                 ORDER BY sus.created_at DESC LIMIT 1";

        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $subscription = mysqli_fetch_assoc($result);

        if ($subscription) {
            $hasActiveSubscription = true;
            $currentSubscription = $subscription;
            $currentPlanId = $subscription['plan_id'];
            $currentPlanAmount = $subscription['plan_amount'];
            $currentPlanInterval = $subscription['plan_interval'];
            $currentPlanName = $subscription['plan_name'];

            // Get current product ID from the plan ID
            $productQuery = "SELECT stripe_product_id FROM stripe_prices WHERE stripe_price_id = ?";
            $productStmt = mysqli_prepare($link, $productQuery);

            if ($productStmt) {
                mysqli_stmt_bind_param($productStmt, "s", $currentPlanId);
                mysqli_stmt_execute($productStmt);
                $productResult = mysqli_stmt_get_result($productStmt);
                $productData = mysqli_fetch_assoc($productResult);

                if ($productData && isset($productData['stripe_product_id'])) {
                    $currentProductId = $productData['stripe_product_id'];
                }
            }

            // Get current product name
            if ($currentProductId) {
                $productNameQuery = "SELECT name FROM stripe_products WHERE stripe_product_id = ?";
                $productNameStmt = mysqli_prepare($link, $productNameQuery);

                if ($productNameStmt) {
                    mysqli_stmt_bind_param($productNameStmt, "s", $currentProductId);
                    mysqli_stmt_execute($productNameStmt);
                    $productNameResult = mysqli_stmt_get_result($productNameStmt);
                    $productNameData = mysqli_fetch_assoc($productNameResult);

                    if ($productNameData && isset($productNameData['name'])) {
                        $currentProductName = $productNameData['name'];
                    }
                }
            }

            // Determine current subscription level
            if ($currentProductName) {
                $currentLevel = determineSubscriptionLevel($currentProductName);
            } else {
                $currentLevel = determineSubscriptionLevel($currentPlanName);
            }

            $currentLevelRank = isset($levels[$currentLevel]) ? $levels[$currentLevel] : 0;

            StripeLogger::log(StripeLogLevel::DEBUG, "CURRENT SUBSCRIPTION DETAILS - User ID: $userId, Plan: $currentPlanName, Product: $currentProductName, Level: $currentLevel, Rank: $currentLevelRank, Amount: $currentPlanAmount, Interval: $currentPlanInterval");
        } else {
            StripeLogger::log(StripeLogLevel::INFO, "NO ACTIVE SUBSCRIPTION - User ID: $userId");
        }

        // Get all available plans
        $plansQuery = "SELECT p.stripe_product_id, p.name as product_name, p.description, p.active,
                      pr.stripe_price_id, pr.currency, pr.unit_amount, pr.`interval`, pr.interval_count
                      FROM stripe_products p
                      JOIN stripe_prices pr ON p.stripe_product_id = pr.stripe_product_id
                      WHERE p.active = 1 AND pr.active = 1
                      ORDER BY pr.`interval`, pr.unit_amount ASC";

        $plansResult = mysqli_query($link, $plansQuery);

        if (!$plansResult) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        $allPlans = [];

        while ($row = mysqli_fetch_assoc($plansResult)) {
            // Determine plan level
            $planLevel = determineSubscriptionLevel($row['product_name']);
            $planLevelRank = isset($levels[$planLevel]) ? $levels[$planLevel] : 0;

            // Format price for display (convert from cents to dollars/TL)
            $formattedPrice = number_format($row['unit_amount'] , 2);

            // Determine plan type based on current subscription
            $planType = 'available'; // Default for users without subscription
            $priceDifference = 0;
            $effectiveDate = null;

            if ($hasActiveSubscription) {
                if ($row['stripe_price_id'] === $currentPlanId) {
                    $planType = 'current';
                } else if ($row['interval'] !== $currentPlanInterval) {
                    $planType = 'other'; // Different billing interval
                } else if ($planLevelRank > $currentLevelRank || ($planLevelRank == $currentLevelRank && $row['unit_amount'] > $currentPlanAmount)) {
                    $planType = 'upgrade';
                    $priceDifference = $row['unit_amount'] - $currentPlanAmount;
                    // For upgrades, no effective date (immediate)
                } else if ($planLevelRank < $currentLevelRank || ($planLevelRank == $currentLevelRank && $row['unit_amount'] < $currentPlanAmount)) {
                    $planType = 'downgrade';
                    $priceDifference = $row['unit_amount'] - $currentPlanAmount; // Will be negative
                    // For downgrades, effective at end of current period
                    $effectiveDate = isset($currentSubscription['current_period_end']) ?
                        date('Y-m-d', intval($currentSubscription['current_period_end'])) :
                        date('Y-m-d', time() + (30 * 24 * 60 * 60));
                }
            }

            $plan = [
                'id' => $row['stripe_price_id'],
                'product_id' => $row['stripe_product_id'],
                'name' => $row['product_name'],
                'description' => $row['description'],
                'price' => $formattedPrice,
                'price_raw' => $row['unit_amount'],
                'currency' => $row['currency'],
                'interval' => $row['interval'],
                'interval_count' => $row['interval_count'],
                'level' => $planLevel,
                'level_rank' => $planLevelRank,
                'type' => $planType
            ];

            // Add additional fields based on plan type
            if ($planType === 'upgrade' || $planType === 'downgrade') {
                $plan['price_difference'] = number_format($priceDifference / 100, 2);
                $plan['price_difference_raw'] = $priceDifference;
            }

            if ($planType === 'downgrade' && $effectiveDate) {
                $plan['effective_date'] = $effectiveDate;
            }

            $allPlans[] = $plan;

            StripeLogger::log(StripeLogLevel::DEBUG, "PLAN: {$row['product_name']}, Level: $planLevel, Amount: {$row['unit_amount']}, Type: $planType");
        }

        // Prepare the response
        $response = [
            'has_active_subscription' => $hasActiveSubscription
        ];

        if ($hasActiveSubscription) {
            $response['current_subscription'] = [
                'id' => $currentSubscription['id'],
                'stripe_subscription_id' => $currentSubscription['stripe_subscription_id'],
                'plan_id' => $currentPlanId,
                'plan_name' => $currentPlanName,
                'product_name' => $currentProductName,
                'product_id' => $currentProductId,
                'plan_amount' => number_format($currentPlanAmount / 100, 2),
                'plan_amount_raw' => $currentPlanAmount,
                'plan_interval' => $currentPlanInterval,
                'level' => $currentLevel,
                'level_rank' => $currentLevelRank,
                'current_period_end' => isset($currentSubscription['current_period_end']) ?
                    date('Y-m-d', intval($currentSubscription['current_period_end'])) : null
            ];
        }

        $response['plans'] = $allPlans;

        // Add features for all subscription levels
        $language = getSelectedLanguage(); // Get user's preferred language

        $response['features'] = [
            'free' => getSubscriptionFeatures('free', $language),
            'basic' => getSubscriptionFeatures('basic', $language),
            'advance' => getSubscriptionFeatures('advance', $language),
            'premium' => getSubscriptionFeatures('premium', $language)
        ];

        // Add notes about upgrade/downgrade
        $response['notes'] = [
            'upgrade' => 'Upgrade will take effect immediately. You will be charged only the prorated difference for the remainder of your billing period.',
            'downgrade' => 'Downgrade will take effect at the end of your current billing period. No refunds will be issued for the current period.'
        ];

        echo json_encode(new SuccessResult($response));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "GET SUBSCRIPTION OPTIONS ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

/**
 * Get available upgrade options for a user with an active subscription
 *
 * This function returns only plans that are more expensive than the user's current plan
 * and have the same billing interval (monthly/yearly)
 */


function getUpgradeOptions() {
    global $link;

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }

    try {
        // Get user's active subscription
        $query = "SELECT sus.id, sus.stripe_subscription_id, sus.plan_id, sus.plan_name,
                 sus.plan_amount, sus.plan_interval
                 FROM stripe_user_subscriptions sus
                 WHERE sus.user_id = ? AND sus.status IN ('active', 'trialing')
                 ORDER BY sus.created_at DESC LIMIT 1";

        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $subscription = mysqli_fetch_assoc($result);

        if (!$subscription) {
            // No active subscription found
            echo json_encode(new ErrorResult("No active subscription found. Please purchase a subscription first.", 404));
            return;
        }

        // Get current plan details
        $currentPlanAmount = $subscription['plan_amount'];
        $currentPlanInterval = $subscription['plan_interval'];
        $currentPlanName = $subscription['plan_name'];

        // Get current plan product ID
        $currentPlanId = $subscription['plan_id'];

        // Get current product ID from the plan ID
        $productQuery = "SELECT stripe_product_id FROM stripe_prices WHERE stripe_price_id = ?";
        $productStmt = mysqli_prepare($link, $productQuery);

        if (!$productStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($productStmt, "s", $currentPlanId);
        mysqli_stmt_execute($productStmt);
        $productResult = mysqli_stmt_get_result($productStmt);
        $productData = mysqli_fetch_assoc($productResult);

        $currentProductId = null;
        if ($productData && isset($productData['stripe_product_id'])) {
            $currentProductId = $productData['stripe_product_id'];
        }

        // Get current product name
        $currentProductName = null;
        if ($currentProductId) {
            $productNameQuery = "SELECT name FROM stripe_products WHERE stripe_product_id = ?";
            $productNameStmt = mysqli_prepare($link, $productNameQuery);

            if ($productNameStmt) {
                mysqli_stmt_bind_param($productNameStmt, "s", $currentProductId);
                mysqli_stmt_execute($productNameStmt);
                $productNameResult = mysqli_stmt_get_result($productNameStmt);
                $productNameData = mysqli_fetch_assoc($productNameResult);

                if ($productNameData && isset($productNameData['name'])) {
                    $currentProductName = $productNameData['name'];
                }
            }
        }

        // Log current plan details for debugging
        StripeLogger::log(StripeLogLevel::DEBUG, "CURRENT PLAN DETAILS FOR UPGRADE - User ID: $userId, Plan: $currentPlanName, Product: $currentProductName, Product ID: $currentProductId, Amount: $currentPlanAmount, Interval: $currentPlanInterval");

        // Define the hierarchy of subscription levels
        $levels = ['free' => 0, 'basic' => 1, 'advance' => 2, 'premium' => 3];

        // Determine current subscription level based on product name
        $currentLevel = 'free';
        if ($currentProductName) {
            $currentLevel = determineSubscriptionLevel($currentProductName);
        } else {
            $currentLevel = determineSubscriptionLevel($currentPlanName);
        }

        $currentLevelRank = isset($levels[$currentLevel]) ? $levels[$currentLevel] : 0;

        StripeLogger::log(StripeLogLevel::DEBUG, "CURRENT SUBSCRIPTION LEVEL FOR UPGRADE - Level: $currentLevel, Rank: $currentLevelRank");

        // Get all available plans with the same interval
        $plansQuery = "SELECT p.stripe_product_id, p.name as product_name, p.description, p.active,
                      pr.stripe_price_id, pr.currency, pr.unit_amount, pr.`interval`, pr.interval_count
                      FROM stripe_products p
                      JOIN stripe_prices pr ON p.stripe_product_id = pr.stripe_product_id
                      WHERE p.active = 1 AND pr.active = 1
                      AND pr.`interval` = ?
                      ORDER BY pr.unit_amount ASC";

        $plansStmt = mysqli_prepare($link, $plansQuery);

        if (!$plansStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($plansStmt, "s", $currentPlanInterval);

        // Log the query parameters for debugging
        StripeLogger::log(StripeLogLevel::DEBUG, "UPGRADE QUERY PARAMETERS - Interval: $currentPlanInterval");
        mysqli_stmt_execute($plansStmt);
        $plansResult = mysqli_stmt_get_result($plansStmt);

        $upgradePlans = [];
        $rowCount = 0;

        // Process all plans and filter based on subscription level
        while ($row = mysqli_fetch_assoc($plansResult)) {
            // Determine the level of this plan based on product name
            $planLevel = determineSubscriptionLevel($row['product_name']);
            $planLevelRank = isset($levels[$planLevel]) ? $levels[$planLevel] : 0;

            StripeLogger::log(StripeLogLevel::DEBUG, "CHECKING PLAN FOR UPGRADE: {$row['product_name']}, Level: $planLevel, Rank: $planLevelRank, Current Level Rank: $currentLevelRank");

            // Only include plans with a higher level than the current one
            if ($planLevelRank > $currentLevelRank) {
                $rowCount++;
                // Format price for display (convert from cents to dollars/TL)
                $formattedPrice = number_format($row['unit_amount'] / 100, 2);

                $upgradePlans[] = [
                    'id' => $row['stripe_price_id'],
                    'product_id' => $row['stripe_product_id'],
                    'name' => $row['product_name'],
                    'description' => $row['description'],
                    'price' => $formattedPrice,
                    'price_raw' => $row['unit_amount'],
                    'currency' => $row['currency'],
                    'interval' => $row['interval'],
                    'interval_count' => $row['interval_count'],
                    // Calculate price difference
                    'price_difference' => number_format(($row['unit_amount'] - $currentPlanAmount) / 100, 2),
                    'price_difference_raw' => $row['unit_amount'] - $currentPlanAmount,
                    'level' => $planLevel,
                    'level_rank' => $planLevelRank,
                    'current_level_rank' => $currentLevelRank
                ];

                StripeLogger::log(StripeLogLevel::DEBUG, "ADDED UPGRADE PLAN - Plan: {$row['product_name']}, Level: $planLevel, Amount: {$row['unit_amount']}");
            } else {
                StripeLogger::log(StripeLogLevel::DEBUG, "SKIPPED PLAN FOR UPGRADE - Plan: {$row['product_name']}, Level: $planLevel, Amount: {$row['unit_amount']} (not higher than current level)");
            }
        }

        // Log the results
        if ($rowCount > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "FOUND $rowCount UPGRADE OPTIONS for User ID: $userId");
        } else {
            StripeLogger::log(StripeLogLevel::WARNING, "NO UPGRADE OPTIONS FOUND for User ID: $userId with Plan: $currentPlanName, Level: $currentLevel");
        }

        // Log the final number of upgrade options
        StripeLogger::log(StripeLogLevel::INFO, "FINAL UPGRADE OPTIONS COUNT: " . count($upgradePlans));

        // Return the upgrade options along with current subscription info
        echo json_encode(new SuccessResult([
            'current_subscription' => [
                'id' => $subscription['id'],
                'stripe_subscription_id' => $subscription['stripe_subscription_id'],
                'plan_id' => $subscription['plan_id'],
                'plan_name' => $currentPlanName,
                'product_name' => $currentProductName,
                'product_id' => $currentProductId,
                'plan_amount' => number_format($currentPlanAmount / 100, 2),
                'plan_amount_raw' => $currentPlanAmount,
                'plan_interval' => $currentPlanInterval,
                'level' => $currentLevel,
                'level_rank' => $currentLevelRank
            ],
            'upgrade_options' => $upgradePlans
        ]));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "GET UPGRADE OPTIONS ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}
